package agentpool

//go:generate sh -c "GO111MODULE=on mockgen --build_flags=--mod=mod go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool AgentPoolLBBackendpoolReconciler>./mock_$GOPACKAGE/interface.go"

import (
	"context"

	cgerror "go.goms.io/aks/rp/toolkit/categorizederror"
)

type AgentPoolLBBackendpoolReconciler interface {
	DecoupleLBBackendPool(tx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}) *cgerror.CategorizedError
	AssociateLBBackendpool(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError
}
