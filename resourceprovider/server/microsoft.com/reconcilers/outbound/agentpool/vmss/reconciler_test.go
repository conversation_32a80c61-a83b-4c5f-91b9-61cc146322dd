package vmss

import (
	"context"
	"fmt"
	"strings"

	"github.com/Azure/azure-sdk-for-go/services/compute/mgmt/2022-03-01/compute"
	"github.com/Azure/go-autorest/autorest/to"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	v1 "go.goms.io/aks/rp/protos/hcp/types/enums/v1"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/consts"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/tags"
	"go.goms.io/aks/rp/toolkit/azureclients/vmssclient"
	"go.goms.io/aks/rp/toolkit/azureclients/vmssclient/mock_vmssclient"
	cgerror "go.goms.io/aks/rp/toolkit/categorizederror"
	"go.goms.io/aks/rp/toolkit/log"
	"go.goms.io/aks/rp/toolkit/secrets"
	"go.uber.org/mock/gomock"
)

// Test helper types for capturing calls to AssociateLBBackendpoolWithVMSS
type vmssCall struct {
	vmss               *compute.VirtualMachineScaleSet
	backendpoolIDs     map[string]struct{}
	backendpoolIDsIPV6 map[string]struct{}
}

type testVMSSReconciler struct {
	vmssClient vmssclient.Interface
	calls      []vmssCall
}

func (t *testVMSSReconciler) AssociateLBBackendpool(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
	// Use the same logic as the real reconciler but capture the calls
	vmssList, _, err := t.vmssClient.ListVirtualMachineScaleSets(ctx, computeSubscriptionID, resourceGroupName, false)
	if err != nil {
		return err
	}
	for _, vmss := range vmssList {
		vmss := vmss

		// Check if this VMSS has a pool name tag - if not, ignore it (same as real reconciler)
		poolNameTag := tags.GetTagValueWithFallbackToOldKey(vmss.Tags, tags.PoolName, tags.OldPoolName)
		if poolNameTag == nil {
			// VMSS without pool tag is ignored, just like in the real reconciler
			continue
		}

		// Check if this VMSS belongs to an excluded agent pool
		var isExcludedAgentPool bool
		if _, excluded := excludedAgentPoolNames[*poolNameTag]; excluded {
			isExcludedAgentPool = true
		}

		// Filter backend pools based on exclusion - excluded agent pools only join outbound pools
		filteredBackendpoolIDs := agentpool.FilterBackendPoolsForExclusion(ctx, backendpoolIDs, isExcludedAgentPool)
		filteredBackendpoolIDsIPV6 := agentpool.FilterBackendPoolsForExclusion(ctx, backendpoolIDsIPV6, isExcludedAgentPool)

		// Capture the call instead of actually calling AssociateLBBackendpoolWithVMSS
		t.calls = append(t.calls, vmssCall{
			vmss:               &vmss,
			backendpoolIDs:     filteredBackendpoolIDs,
			backendpoolIDsIPV6: filteredBackendpoolIDsIPV6,
		})
	}
	return nil
}

func (t *testVMSSReconciler) DecoupleLBBackendPool(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}) *cgerror.CategorizedError {
	// Not needed for this test
	return nil
}

var _ = Describe("vmss", func() {
	var (
		mockCtrl *gomock.Controller
		ctx      context.Context
		logger   *log.Logger

		vmssClient             *mock_vmssclient.MockInterface
		outboundConn           *VMSSSLBBackendpoolReconciler
		existingVMSS           *compute.VirtualMachineScaleSet
		existingVMSSVM         *compute.VirtualMachineScaleSetVM
		backendIDmaps          map[string]struct{}
		backendIPV6IDmaps      map[string]struct{}
		mixedBackendIDmaps     map[string]struct{}
		mixedBackendIPV6IDmaps map[string]struct{}

		mcResourceGroupName string
		location            string
		subscriptionID      string
		vmssName            string
	)
	BeforeEach(func() {
		mockCtrl = gomock.NewController(GinkgoT())
		ctx = context.Background()
		logger = log.InitializeTestLogger()
		ctx = log.WithLogger(context.Background(), logger)
		apiTracking := log.NewAPITrackingFromParametersMap(nil)
		ctx = log.WithAPITracking(ctx, apiTracking)

		vmssClient = mock_vmssclient.NewMockInterface(mockCtrl)

		location = "westus"
		subscriptionID = "b8c5d784-96a1-4e65-9acb-9246b26c8888"
		mcResourceGroupName = "mc_testingrg_testcluster"
		vmssName = "vmss1"

		backendIDmaps = map[string]struct{}{
			strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolName)): {},
		}
		backendIPV6IDmaps = map[string]struct{}{
			strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolNameIPv6)): {},
		}
		// Mixed backend pools include both inbound and outbound pools for testing exclusion logic
		mixedBackendIDmaps = map[string]struct{}{
			strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolName)): {},
			strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, "kubernetes")):                      {},
		}
		mixedBackendIPV6IDmaps = map[string]struct{}{
			strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolNameIPv6)): {},
			strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, "kubernetes-ipv6")):                     {},
		}
		outboundConn = &VMSSSLBBackendpoolReconciler{
			vmssClient: vmssClient,
		}

		pass, _ := secrets.NewWindowsCredentialGenerator().GenerateWindowsPassword()
		slbName := consts.SLBName

		existingVMSS = &compute.VirtualMachineScaleSet{
			Name:     to.StringPtr(vmssName),
			Tags:     map[string]*string{tags.PoolName: to.StringPtr("test")},
			ID:       to.StringPtr(fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Compute/virtualMachineScaleSets/%s", subscriptionID, mcResourceGroupName, "vmss1")),
			Location: to.StringPtr(location),
			Sku: &compute.Sku{
				Name:     to.StringPtr("Standard_F1s"),
				Tier:     to.StringPtr("Standard"),
				Capacity: to.Int64Ptr(int64(3)),
			},
			VirtualMachineScaleSetProperties: &compute.VirtualMachineScaleSetProperties{
				Overprovision: to.BoolPtr(false),
				UpgradePolicy: &compute.UpgradePolicy{
					Mode: compute.UpgradeModeManual,
					AutomaticOSUpgradePolicy: &compute.AutomaticOSUpgradePolicy{
						EnableAutomaticOSUpgrade: to.BoolPtr(false),
					},
				},
				VirtualMachineProfile: &compute.VirtualMachineScaleSetVMProfile{
					OsProfile: &compute.VirtualMachineScaleSetOSProfile{
						ComputerNamePrefix: to.StringPtr(vmssName),
						AdminUsername:      to.StringPtr("azureuser"),
						AdminPassword:      to.StringPtr(string(pass)),
					},
					StorageProfile: &compute.VirtualMachineScaleSetStorageProfile{
						ImageReference: &compute.ImageReference{
							Offer:     to.StringPtr("UbuntuServer"),
							Publisher: to.StringPtr("Canonical"),
							Sku:       to.StringPtr("16.04.0-LTS"),
							Version:   to.StringPtr("latest"),
						},
					},
					NetworkProfile: &compute.VirtualMachineScaleSetNetworkProfile{
						NetworkInterfaceConfigurations: &[]compute.VirtualMachineScaleSetNetworkConfiguration{
							{
								Name: to.StringPtr(vmssName),
								VirtualMachineScaleSetNetworkConfigurationProperties: &compute.VirtualMachineScaleSetNetworkConfigurationProperties{
									Primary:            to.BoolPtr(true),
									EnableIPForwarding: to.BoolPtr(true),
									IPConfigurations: &[]compute.VirtualMachineScaleSetIPConfiguration{
										{
											Name: to.StringPtr(vmssName),
											VirtualMachineScaleSetIPConfigurationProperties: &compute.VirtualMachineScaleSetIPConfigurationProperties{
												Primary: to.BoolPtr(true),
												LoadBalancerBackendAddressPools: &[]compute.SubResource{
													{
														ID: to.StringPtr(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, slbName, "mgr")),
													},
													{
														ID: to.StringPtr(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, slbName, consts.SlbOutboundBackendPoolName)),
													},
												},
											},
										},
										{
											Name: to.StringPtr(vmssName + "-ipv6"),
											VirtualMachineScaleSetIPConfigurationProperties: &compute.VirtualMachineScaleSetIPConfigurationProperties{

												PrivateIPAddressVersion: compute.IPv6,
												LoadBalancerBackendAddressPools: &[]compute.SubResource{
													{
														ID: to.StringPtr(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, slbName, consts.SlbOutboundBackendPoolNameIPv6)),
													},
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		}
		existingVMSSVM = &compute.VirtualMachineScaleSetVM{
			ID:         to.StringPtr(fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Compute/virtualMachineScaleSets/%s/virtualMachines/%s", subscriptionID, mcResourceGroupName, "vmss1", "0")),
			InstanceID: to.StringPtr("0"),
			Name:       to.StringPtr("0"),
			VirtualMachineScaleSetVMProperties: &compute.VirtualMachineScaleSetVMProperties{
				ProvisioningState: to.StringPtr("Succeeded"),
				OsProfile: &compute.OSProfile{
					AdminUsername: to.StringPtr("azureuser"),
					AdminPassword: to.StringPtr(string(pass)),
				},
				InstanceView: &compute.VirtualMachineScaleSetVMInstanceView{
					Statuses: &[]compute.InstanceViewStatus{
						{
							Code: to.StringPtr("PowerState/Running"),
						},
					},
				},
				NetworkProfileConfiguration: &compute.VirtualMachineScaleSetVMNetworkProfileConfiguration{
					NetworkInterfaceConfigurations: &[]compute.VirtualMachineScaleSetNetworkConfiguration{
						{
							Name: to.StringPtr(vmssName),
							VirtualMachineScaleSetNetworkConfigurationProperties: &compute.VirtualMachineScaleSetNetworkConfigurationProperties{
								Primary:            to.BoolPtr(true),
								EnableIPForwarding: to.BoolPtr(true),
								IPConfigurations: &[]compute.VirtualMachineScaleSetIPConfiguration{
									{
										Name: to.StringPtr(vmssName),
										VirtualMachineScaleSetIPConfigurationProperties: &compute.VirtualMachineScaleSetIPConfigurationProperties{
											Primary: to.BoolPtr(true),
											LoadBalancerBackendAddressPools: &[]compute.SubResource{
												{
													ID: to.StringPtr(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, slbName, consts.SlbOutboundBackendPoolName)),
												},
												{
													ID: to.StringPtr(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, slbName, "mgr")),
												},
											},
										},
									},
									{
										Name: to.StringPtr(vmssName + "-ipv6"),
										VirtualMachineScaleSetIPConfigurationProperties: &compute.VirtualMachineScaleSetIPConfigurationProperties{

											PrivateIPAddressVersion: compute.IPv6,
											LoadBalancerBackendAddressPools: &[]compute.SubResource{
												{
													ID: to.StringPtr(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, slbName, consts.SlbOutboundBackendPoolNameIPv6)),
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		}
	})

	AfterEach(func() {
		mockCtrl.Finish()
	})

	Context("associate vmss with backend pools", func() {
		When("list vm returned error", func() {
			It("should return error", func() {
				vmssClient.EXPECT().ListVirtualMachineScaleSets(gomock.Any(), subscriptionID, mcResourceGroupName, false).Return(nil, nil, cgerror.ToCategorizedError(fmt.Errorf("error")))
				cerr := outboundConn.AssociateLBBackendpool(ctx, subscriptionID, subscriptionID, mcResourceGroupName, nil, nil, map[string]struct{}{})
				Expect(cerr).NotTo(BeNil())
			})
		})
		When("list vm returned error", func() {
			It("should return error", func() {
				existingVMSS.VirtualMachineScaleSetProperties = nil
				vmssClient.EXPECT().ListVirtualMachineScaleSets(gomock.Any(), subscriptionID, mcResourceGroupName, false).Return([]compute.VirtualMachineScaleSet{*existingVMSS}, nil, nil)
				cerr := outboundConn.AssociateLBBackendpool(ctx, subscriptionID, subscriptionID, mcResourceGroupName, nil, nil, map[string]struct{}{})
				Expect(cerr).NotTo(BeNil())
				Expect(cerr.Error()).To(ContainSubstring("read the network interface configuration"))
			})
		})
		When("vmss is not valid", func() {
			It("should return error", func() {
				existingVMSS.VirtualMachineScaleSetProperties = nil
				cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
				Expect(cerr.Error()).To(ContainSubstring("read the network interface configuration"))
			})
		})
		When("vmss is being deleted", func() {
			It("should return error", func() {
				existingVMSS.ProvisioningState = to.StringPtr("Deallocating")
				cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).To(BeNil())
			})
		})
		When("vmss does not have nic config", func() {
			It("should return error", func() {
				*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations = nil
				cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
				Expect(cerr.Error()).To(ContainSubstring("failed to find a primary network configuration"))
			})
		})
		When("vmss does not have primary nic config", func() {
			It("should return error", func() {
				(*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations)[0].Primary = to.BoolPtr(false)
				(*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations) = append((*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations), (*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations)[0])
				cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
				Expect(cerr.Error()).To(ContainSubstring("failed to find a primary network configuration"))
			})
		})
		When("vmss does not have ip config", func() {
			It("should return error", func() {
				(*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations)[0].IPConfigurations = nil
				cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
				Expect(cerr.Error()).To(ContainSubstring("vmssNetworkConfig.IPConfigurations is nil"))
			})
		})
		When("vmss does not have primary ip config", func() {
			It("should return error", func() {
				(*(*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools = nil
				vmssClient.EXPECT().CreateOrUpdateVirtualMachineScaleSet(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, gomock.Any()).DoAndReturn(
					func(ctx context.Context, subscriptionID string, resourceGroup string, virtualMachineScaleSetName string, vmss *compute.VirtualMachineScaleSet) (*compute.VirtualMachineScaleSet, *cgerror.CategorizedError) {
						backendpoolconfig := (*(*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools
						Expect(backendpoolconfig).NotTo(BeNil())
						Expect(*(*backendpoolconfig)[0].ID).To(Equal(strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolName))))
						return nil, cgerror.CreateInternalError()
					})
				cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
			})
		})
		When("ipconfig is nil", func() {
			It("should return error", func() {
				(*(*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].Primary = to.BoolPtr(false)
				(*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations) = append((*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations), (*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations)[0])
				cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
				Expect(cerr.Error()).To(ContainSubstring("failed to find a primary IP configuration"))
			})
		})
		When("vmss is not managed", func() {
			It("should not create new vmss", func() {
				existingVMSS.Tags = nil
				cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).To(BeNil())
			})
		})
		When("vmss is valid", func() {
			It("should not create new vmss", func() {
				backendIPV6IDmaps = nil
				(*(*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools = &[]compute.SubResource{
					{
						ID: to.StringPtr(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolName)),
					},
				}
				vmssClient.EXPECT().ListVirtualMachineScaleSetVMsWithInstanceViews(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName).Return([]compute.VirtualMachineScaleSetVM{*existingVMSSVM}, nil)
				cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).To(BeNil())
			})
		})
		When("vmss is valid", func() {
			It("should not return error", func() {
				vmssClient.EXPECT().ListVirtualMachineScaleSetVMsWithInstanceViews(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName).Return([]compute.VirtualMachineScaleSetVM{*existingVMSSVM}, nil)
				cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).To(BeNil())
			})
		})
		When("vm returned error", func() {
			It("should return error", func() {
				vmssClient.EXPECT().ListVirtualMachineScaleSetVMsWithInstanceViews(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName).Return(nil, cgerror.CreateInternalError())
				cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
			})
		})
		Context("vmss vm", func() {
			BeforeEach(func() {
				vmssClient.EXPECT().ListVirtualMachineScaleSetVMsWithInstanceViews(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName).Return([]compute.VirtualMachineScaleSetVM{*existingVMSSVM}, nil)
			})

			When("vmss vm is not valid", func() {
				It("should not return error", func() {
					existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations = nil
					cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).NotTo(BeNil())
				})
			})
			When("vmss vm ipconfig is not valid", func() {
				It("should not return error", func() {
					(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations = nil
					cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).NotTo(BeNil())
				})
			})
			When("vmss vm is being deleted", func() {
				It("should not return error", func() {
					existingVMSSVM.ProvisioningState = to.StringPtr("Deallocating")
					cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
			})
			When("vmss vm is being deleted", func() {
				It("should not return error", func() {
					existingVMSSVM.ProvisioningState = to.StringPtr("Deallocating")
					cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
			})
			When("vmss vm instance view is nil", func() {
				It("should not return error", func() {
					correctVMSSVM := *existingVMSSVM
					props := (*existingVMSSVM.VirtualMachineScaleSetVMProperties)
					correctVMSSVM.VirtualMachineScaleSetVMProperties = &props
					existingVMSSVM.InstanceView = nil
					vmssClient.EXPECT().GetVirtualMachineScaleSetVMWithInstanceViews(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, "0").Return(&correctVMSSVM, nil)
					cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
			})
			When("vmss vm instance view is nil", func() {
				It("should not return error", func() {
					existingVMSSVM.InstanceView = nil
					vmssClient.EXPECT().GetVirtualMachineScaleSetVMWithInstanceViews(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, "0").Return(nil, cgerror.CreateInternalError())
					cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).NotTo(BeNil())
				})
			})
			When("vmss vm is stopped", func() {
				It("should not return error", func() {
					(*existingVMSSVM.InstanceView.Statuses)[0].Code = to.StringPtr("Stopped")
					cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
			})
			When("vmss vm is valid", func() {
				It("should not return error", func() {
					cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
			})
			When("vmss vm is valid", func() {
				It("should not return error", func() {
					existingVMSSVM.ProvisioningState = to.StringPtr("Succeeded")
					(*(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools = nil
					(*(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations)[1].LoadBalancerBackendAddressPools = nil

					vmssClient.EXPECT().UpdateVirtualMachineScaleSetVM(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, "0", gomock.Any()).DoAndReturn(
						func(ctx context.Context, subscriptionID string, resourceGroup string, virtualMachineScaleSetName string, vmInstanceID string, vm *compute.VirtualMachineScaleSetVM) (*compute.VirtualMachineScaleSetVM, *cgerror.CategorizedError) {
							backendpoolconfig := (*(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools
							Expect(backendpoolconfig).NotTo(BeNil())
							Expect(*(*backendpoolconfig)[0].ID).To(Equal(strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolName))))
							ipv6Backendpoolconfig := (*(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations)[1].LoadBalancerBackendAddressPools
							Expect(ipv6Backendpoolconfig).NotTo(BeNil())
							Expect(*(*ipv6Backendpoolconfig)[0].ID).To(Equal(strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolNameIPv6))))
							return existingVMSSVM, nil
						})
					cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
				It("should not return error", func() {
					existingVMSSVM.ProvisioningState = to.StringPtr("Succeeded")
					(*(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools = nil
					(*(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations)[1].LoadBalancerBackendAddressPools = nil

					vmssClient.EXPECT().UpdateVirtualMachineScaleSetVM(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, "0", gomock.Any()).DoAndReturn(
						func(ctx context.Context, subscriptionID string, resourceGroup string, virtualMachineScaleSetName string, vmInstanceID string, vm *compute.VirtualMachineScaleSetVM) (*compute.VirtualMachineScaleSetVM, *cgerror.CategorizedError) {
							backendpoolconfig := (*(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools
							Expect(backendpoolconfig).NotTo(BeNil())
							Expect(*(*backendpoolconfig)[0].ID).To(Equal(strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolName))))
							ipv6Backendpoolconfig := (*(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations)[1].LoadBalancerBackendAddressPools
							Expect(ipv6Backendpoolconfig).NotTo(BeNil())
							Expect(*(*ipv6Backendpoolconfig)[0].ID).To(Equal(strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolNameIPv6))))
							return nil, &cgerror.CategorizedError{
								SubCode: cgerror.InvalidParameter,
								OriginError: &cgerror.CategorizedError{
									Code:      v1.ErrorCode_InvalidParameter,
									Message:   "The provided instanceId 32 is not an active Virtual Machine Scale Set VM instanceId.",
									Target:    "instanceIds",
									Retriable: to.BoolPtr(false),
								},
							}
						})
					cerr := outboundConn.AssociateLBBackendpoolWithVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
			})
		})
	})
	Context("disassociate vmss with backend pools", func() {
		When("list vm returned error", func() {
			It("should return error", func() {
				vmssClient.EXPECT().ListVirtualMachineScaleSets(gomock.Any(), subscriptionID, mcResourceGroupName, false).Return(nil, nil, cgerror.ToCategorizedError(fmt.Errorf("error")))
				cerr := outboundConn.DecoupleLBBackendPool(ctx, subscriptionID, subscriptionID, mcResourceGroupName, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
			})
		})
		When("vmss is not valid", func() {
			It("should return error", func() {
				existingVMSS.VirtualMachineScaleSetProperties = nil
				vmssClient.EXPECT().ListVirtualMachineScaleSets(gomock.Any(), subscriptionID, mcResourceGroupName, false).Return([]compute.VirtualMachineScaleSet{*existingVMSS}, nil, nil)
				cerr := outboundConn.DecoupleLBBackendPool(ctx, subscriptionID, subscriptionID, mcResourceGroupName, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
				Expect(cerr.Error()).To(ContainSubstring("read the network interface configuration"))
			})
		})
		When("vmss is not valid", func() {
			It("should return error", func() {
				existingVMSS.VirtualMachineScaleSetProperties = nil
				cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
				Expect(cerr.Error()).To(ContainSubstring("read the network interface configuration"))
			})
		})
		When("vmss is being deleted", func() {
			It("should ignore error", func() {
				existingVMSS.ProvisioningState = to.StringPtr("Deallocating")
				cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).To(BeNil())
			})
		})
		When("vmss does not have nic config", func() {
			It("should return error", func() {
				existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations = nil
				cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
				Expect(cerr.Error()).To(ContainSubstring("failed to read the network interface configuration"))
			})
		})
		When("vmss does not have ip config", func() {
			It("should not return error", func() {
				vmssClient.EXPECT().ListVirtualMachineScaleSetVMsWithInstanceViews(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName).Return(nil, nil)
				(*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations)[0].IPConfigurations = nil

				cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).To(BeNil())
			})
		})
		When("vmss is not managed", func() {
			It("should return nil", func() {
				existingVMSS.Tags = nil
				cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).To(BeNil())
			})
		})
		When("vmss is valid", func() {
			It("should return nil", func() {
				vmssClient.EXPECT().CreateOrUpdateVirtualMachineScaleSet(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, gomock.Any()).DoAndReturn(
					func(ctx context.Context, subscriptionID string, resourceGroup string, virtualMachineScaleSetName string, vmss *compute.VirtualMachineScaleSet) (*compute.VirtualMachineScaleSet, *cgerror.CategorizedError) {
						backendpoolconfig := (*(*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools
						Expect(*backendpoolconfig).To(HaveLen(1))
						return nil, cgerror.CreateInternalError()
					})
				cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
			})
		})
		When("vmss vm is not valid", func() {
			It("should not return error", func() {
				vmssClient.EXPECT().CreateOrUpdateVirtualMachineScaleSet(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, gomock.Any()).DoAndReturn(
					func(ctx context.Context, subscriptionID string, resourceGroup string, virtualMachineScaleSetName string, vmss *compute.VirtualMachineScaleSet) (*compute.VirtualMachineScaleSet, *cgerror.CategorizedError) {
						backendpoolconfig := (*(*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools
						Expect(*backendpoolconfig).To(HaveLen(1))
						return nil, nil
					})
				vmssClient.EXPECT().ListVirtualMachineScaleSetVMsWithInstanceViews(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName).Return(nil, cgerror.CreateInternalError())

				cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
				Expect(cerr).NotTo(BeNil())
			})
		})
		Context("vmss vm is not valid", func() {
			BeforeEach(func() {
				vmssClient.EXPECT().ListVirtualMachineScaleSetVMsWithInstanceViews(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName).Return([]compute.VirtualMachineScaleSetVM{*existingVMSSVM}, nil)
				vmssClient.EXPECT().CreateOrUpdateVirtualMachineScaleSet(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, gomock.Any()).DoAndReturn(
					func(ctx context.Context, subscriptionID string, resourceGroup string, virtualMachineScaleSetName string, vmss *compute.VirtualMachineScaleSet) (*compute.VirtualMachineScaleSet, *cgerror.CategorizedError) {
						backendpoolconfig := (*(*existingVMSS.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools
						Expect(*backendpoolconfig).To(HaveLen(1))
						return existingVMSS, nil
					})
			})

			When("vmss vm is not valid", func() {
				It("should not return error", func() {
					existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations = nil
					cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).NotTo(BeNil())
				})
			})
			When("vmss vm is being deleted", func() {
				It("should not return error", func() {
					existingVMSSVM.ProvisioningState = to.StringPtr("Deallocating")
					cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
			})
			When("vmss vm ipconfig is not valid", func() {
				It("should not return error", func() {
					(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations = nil
					cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
			})
			When("vmss vm is stopped", func() {
				It("should not return error", func() {
					(*existingVMSSVM.InstanceView.Statuses)[0].Code = to.StringPtr("Stopped")
					cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
			})
			When("vmss vm is nil", func() {
				It("should not return error", func() {
					correctVMSSVM := *existingVMSSVM
					props := (*existingVMSSVM.VirtualMachineScaleSetVMProperties)
					correctVMSSVM.VirtualMachineScaleSetVMProperties = &props
					existingVMSSVM.InstanceView = nil
					vmssClient.EXPECT().GetVirtualMachineScaleSetVMWithInstanceViews(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, "0").Return(&correctVMSSVM, nil)
					vmssClient.EXPECT().UpdateVirtualMachineScaleSetVM(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, "0", gomock.Any()).DoAndReturn(
						func(ctx context.Context, subscriptionID string, resourceGroup string, virtualMachineScaleSetName string, vmInstanceID string, vm *compute.VirtualMachineScaleSetVM) (*compute.VirtualMachineScaleSetVM, *cgerror.CategorizedError) {
							backendpoolconfig := (*(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools
							Expect(*backendpoolconfig).To(HaveLen(1))
							return existingVMSSVM, nil
						})
					cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
			})
			When("vmss vm is nil", func() {
				It("should return error when failed to get vm instance view", func() {
					existingVMSSVM.InstanceView = nil
					vmssClient.EXPECT().GetVirtualMachineScaleSetVMWithInstanceViews(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, "0").Return(nil, cgerror.CreateInternalError())
					cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).NotTo(BeNil())
				})
			})
			When("vmss vm is valid", func() {
				It("should not return error", func() {
					vmssClient.EXPECT().UpdateVirtualMachineScaleSetVM(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, "0", gomock.Any()).DoAndReturn(
						func(ctx context.Context, subscriptionID string, resourceGroup string, virtualMachineScaleSetName string, vmInstanceID string, vm *compute.VirtualMachineScaleSetVM) (*compute.VirtualMachineScaleSetVM, *cgerror.CategorizedError) {
							backendpoolconfig := (*(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools
							Expect(*backendpoolconfig).To(HaveLen(1))
							return existingVMSSVM, nil
						})

					cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
				It("should return error", func() {
					vmssClient.EXPECT().UpdateVirtualMachineScaleSetVM(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, "0", gomock.Any()).DoAndReturn(
						func(ctx context.Context, subscriptionID string, resourceGroup string, virtualMachineScaleSetName string, vmInstanceID string, vm *compute.VirtualMachineScaleSetVM) (*compute.VirtualMachineScaleSetVM, *cgerror.CategorizedError) {
							backendpoolconfig := (*(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools
							Expect(*backendpoolconfig).To(HaveLen(1))
							return nil, cgerror.CreateInternalError()
						})

					cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).NotTo(BeNil())
				})
				It("should not return error", func() {
					vmssClient.EXPECT().UpdateVirtualMachineScaleSetVM(gomock.Any(), subscriptionID, mcResourceGroupName, vmssName, "0", gomock.Any()).DoAndReturn(
						func(ctx context.Context, subscriptionID string, resourceGroup string, virtualMachineScaleSetName string, vmInstanceID string, vm *compute.VirtualMachineScaleSetVM) (*compute.VirtualMachineScaleSetVM, *cgerror.CategorizedError) {
							backendpoolconfig := (*(*existingVMSSVM.NetworkProfileConfiguration.NetworkInterfaceConfigurations)[0].IPConfigurations)[0].LoadBalancerBackendAddressPools
							Expect(*backendpoolconfig).To(HaveLen(1))
							return nil, &cgerror.CategorizedError{
								SubCode: cgerror.InvalidParameter,
								OriginError: &cgerror.CategorizedError{
									Code:      v1.ErrorCode_InvalidParameter,
									Message:   "The provided instanceId 32 is not an active Virtual Machine Scale Set VM instanceId.",
									Target:    "instanceIds",
									Retriable: to.BoolPtr(false),
								},
							}
						})
					cerr := outboundConn.DecoupleLBBackendPoolFromVMSS(ctx, subscriptionID, mcResourceGroupName, existingVMSS, backendIDmaps, backendIPV6IDmaps)
					Expect(cerr).To(BeNil())
				})
			})
		})

		Context("associate vmss with backend pools - excluded agent pools", func() {
			// Helper function to create VMSS with specific pool name tag
			createVMSSWithPoolTag := func(poolName string) *compute.VirtualMachineScaleSet {
				vmss := *existingVMSS // Copy the existing VMSS
				vmss.Tags = map[string]*string{tags.PoolName: to.StringPtr(poolName)}
				return &vmss
			}

			When("no agent pools are excluded", func() {
				It("should pass all backend pools to all VMSS", func() {
					normalVMSS := createVMSSWithPoolTag("normal-pool")
					testReconciler := &testVMSSReconciler{
						vmssClient: vmssClient,
						calls:      make([]vmssCall, 0),
					}

					vmssClient.EXPECT().ListVirtualMachineScaleSets(gomock.Any(), subscriptionID, mcResourceGroupName, false).Return([]compute.VirtualMachineScaleSet{*normalVMSS}, nil, nil)

					cerr := testReconciler.AssociateLBBackendpool(ctx, subscriptionID, subscriptionID, mcResourceGroupName, mixedBackendIDmaps, mixedBackendIPV6IDmaps, map[string]struct{}{})
					Expect(cerr).To(BeNil())

					// Verify that normal VMSS gets all backend pools (both inbound and outbound)
					Expect(testReconciler.calls).To(HaveLen(1))
					call := testReconciler.calls[0]
					Expect(call.backendpoolIDs).To(HaveLen(2))     // Should have both outbound and inbound pools
					Expect(call.backendpoolIDsIPV6).To(HaveLen(2)) // Should have both outbound and inbound IPv6 pools
				})
			})

			When("some agent pools are excluded", func() {
				It("should filter backend pools for excluded VMSS", func() {
					excludedVMSS := createVMSSWithPoolTag("excluded-pool")
					normalVMSS := createVMSSWithPoolTag("normal-pool")

					excludedAgentPools := map[string]struct{}{
						"excluded-pool": {},
					}

					testReconciler := &testVMSSReconciler{
						vmssClient: vmssClient,
						calls:      make([]vmssCall, 0),
					}

					vmssClient.EXPECT().ListVirtualMachineScaleSets(gomock.Any(), subscriptionID, mcResourceGroupName, false).Return([]compute.VirtualMachineScaleSet{*excludedVMSS, *normalVMSS}, nil, nil)

					cerr := testReconciler.AssociateLBBackendpool(ctx, subscriptionID, subscriptionID, mcResourceGroupName, mixedBackendIDmaps, mixedBackendIPV6IDmaps, excludedAgentPools)
					Expect(cerr).To(BeNil())

					// Verify we have calls for both VMSS
					Expect(testReconciler.calls).To(HaveLen(2))

					// Find the excluded and normal VMSS calls
					var excludedCall, normalCall *vmssCall
					for i := range testReconciler.calls {
						poolTag := tags.GetTagValueWithFallbackToOldKey(testReconciler.calls[i].vmss.Tags, tags.PoolName, tags.OldPoolName)
						if poolTag != nil && *poolTag == "excluded-pool" {
							excludedCall = &testReconciler.calls[i]
						} else if poolTag != nil && *poolTag == "normal-pool" {
							normalCall = &testReconciler.calls[i]
						}
					}

					Expect(excludedCall).NotTo(BeNil())
					Expect(normalCall).NotTo(BeNil())

					// Excluded VMSS should only get outbound pools
					Expect(excludedCall.backendpoolIDs).To(HaveLen(1))
					Expect(excludedCall.backendpoolIDsIPV6).To(HaveLen(1))

					// Normal VMSS should get all pools
					Expect(normalCall.backendpoolIDs).To(HaveLen(2))
					Expect(normalCall.backendpoolIDsIPV6).To(HaveLen(2))
				})

				It("should verify excluded VMSS only gets outbound pools", func() {
					excludedVMSS := createVMSSWithPoolTag("excluded-pool")
					excludedAgentPools := map[string]struct{}{
						"excluded-pool": {},
					}

					testReconciler := &testVMSSReconciler{
						vmssClient: vmssClient,
						calls:      make([]vmssCall, 0),
					}

					vmssClient.EXPECT().ListVirtualMachineScaleSets(gomock.Any(), subscriptionID, mcResourceGroupName, false).Return([]compute.VirtualMachineScaleSet{*excludedVMSS}, nil, nil)

					cerr := testReconciler.AssociateLBBackendpool(ctx, subscriptionID, subscriptionID, mcResourceGroupName, mixedBackendIDmaps, mixedBackendIPV6IDmaps, excludedAgentPools)
					Expect(cerr).To(BeNil())

					// Verify that the excluded VMSS only received outbound pools
					Expect(testReconciler.calls).To(HaveLen(1))
					call := testReconciler.calls[0]

					// Should only have outbound pools (not inbound pools like "kubernetes")
					Expect(call.backendpoolIDs).To(HaveLen(1))
					Expect(call.backendpoolIDsIPV6).To(HaveLen(1))

					// Verify it contains outbound pool
					outboundPoolID := strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolName))
					Expect(call.backendpoolIDs).To(HaveKey(outboundPoolID))

					// Verify it does NOT contain inbound pool
					inboundPoolID := strings.ToLower(fmt.Sprintf(`/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/backendAddressPools/%s`, subscriptionID, mcResourceGroupName, consts.SLBName, "kubernetes"))
					Expect(call.backendpoolIDs).NotTo(HaveKey(inboundPoolID))
				})

				It("should verify VMSS with no pool tag is ignored", func() {
					vmssWithoutTag := *existingVMSS
					vmssWithoutTag.Tags = nil

					excludedAgentPools := map[string]struct{}{
						"excluded-pool": {},
					}

					testReconciler := &testVMSSReconciler{
						vmssClient: vmssClient,
						calls:      make([]vmssCall, 0),
					}

					vmssClient.EXPECT().ListVirtualMachineScaleSets(gomock.Any(), subscriptionID, mcResourceGroupName, false).Return([]compute.VirtualMachineScaleSet{vmssWithoutTag}, nil, nil)

					cerr := testReconciler.AssociateLBBackendpool(ctx, subscriptionID, subscriptionID, mcResourceGroupName, mixedBackendIDmaps, mixedBackendIPV6IDmaps, excludedAgentPools)
					Expect(cerr).To(BeNil())

					// VMSS without pool tag should be ignored - no calls should be made
					Expect(testReconciler.calls).To(HaveLen(0))
				})
			})
		})
	})
})
