// Code generated by MockGen. DO NOT EDIT.
// Source: go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool (interfaces: AgentPoolLBBackendpoolReconciler)
//
// Generated by this command:
//
//	mockgen --build_flags=--mod=mod go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool AgentPoolLBBackendpoolReconciler
//

// Package mock_agentpool is a generated GoMock package.
package mock_agentpool

import (
	context "context"
	reflect "reflect"

	categorizederror "go.goms.io/aks/rp/toolkit/categorizederror"
	gomock "go.uber.org/mock/gomock"
)

// MockAgentPoolLBBackendpoolReconciler is a mock of AgentPoolLBBackendpoolReconciler interface.
type MockAgentPoolLBBackendpoolReconciler struct {
	ctrl     *gomock.Controller
	recorder *MockAgentPoolLBBackendpoolReconcilerMockRecorder
}

// MockAgentPoolLBBackendpoolReconcilerMockRecorder is the mock recorder for MockAgentPoolLBBackendpoolReconciler.
type MockAgentPoolLBBackendpoolReconcilerMockRecorder struct {
	mock *MockAgentPoolLBBackendpoolReconciler
}

// NewMockAgentPoolLBBackendpoolReconciler creates a new mock instance.
func NewMockAgentPoolLBBackendpoolReconciler(ctrl *gomock.Controller) *MockAgentPoolLBBackendpoolReconciler {
	mock := &MockAgentPoolLBBackendpoolReconciler{ctrl: ctrl}
	mock.recorder = &MockAgentPoolLBBackendpoolReconcilerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAgentPoolLBBackendpoolReconciler) EXPECT() *MockAgentPoolLBBackendpoolReconcilerMockRecorder {
	return m.recorder
}

// AssociateLBBackendpool mocks base method.
func (m *MockAgentPoolLBBackendpoolReconciler) AssociateLBBackendpool(arg0 context.Context, arg1, arg2, arg3 string, arg4, arg5, arg6 map[string]struct{}) *categorizederror.CategorizedError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssociateLBBackendpool", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(*categorizederror.CategorizedError)
	return ret0
}

// AssociateLBBackendpool indicates an expected call of AssociateLBBackendpool.
func (mr *MockAgentPoolLBBackendpoolReconcilerMockRecorder) AssociateLBBackendpool(arg0, arg1, arg2, arg3, arg4, arg5, arg6 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateLBBackendpool", reflect.TypeOf((*MockAgentPoolLBBackendpoolReconciler)(nil).AssociateLBBackendpool), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// DecoupleLBBackendPool mocks base method.
func (m *MockAgentPoolLBBackendpoolReconciler) DecoupleLBBackendPool(arg0 context.Context, arg1, arg2, arg3 string, arg4, arg5 map[string]struct{}) *categorizederror.CategorizedError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecoupleLBBackendPool", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*categorizederror.CategorizedError)
	return ret0
}

// DecoupleLBBackendPool indicates an expected call of DecoupleLBBackendPool.
func (mr *MockAgentPoolLBBackendpoolReconcilerMockRecorder) DecoupleLBBackendPool(arg0, arg1, arg2, arg3, arg4, arg5 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecoupleLBBackendPool", reflect.TypeOf((*MockAgentPoolLBBackendpoolReconciler)(nil).DecoupleLBBackendPool), arg0, arg1, arg2, arg3, arg4, arg5)
}
