package slb

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork/v5"
	"github.com/Azure/azure-sdk-for-go/services/network/mgmt/2022-07-01/network"
	"github.com/Azure/go-autorest/autorest/to"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"go.uber.org/mock/gomock"

	"go.goms.io/aks/rp/devinfraservices/toggles/rollout"
	"go.goms.io/aks/rp/devinfraservices/toggles/rollout/fake"
	hcpAgentPool "go.goms.io/aks/rp/protos/hcp/types/agentpool/v1"
	hcpEnums "go.goms.io/aks/rp/protos/hcp/types/enums/v1"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/asyncoperationsprocessor/rawgoalretriever/mock_rawgoalretriever"
	rpcommonconsts "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/common/consts"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/containerservice/flags"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/goalresolvers"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool/mock_agentpool"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/utils"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/azureresources/adapters"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/azureresources/mock_azureresources"
	azureresources_test "go.goms.io/aks/rp/resourceprovider/sharedlib/azureresources/test"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/consts"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/tags"
	"go.goms.io/aks/rp/toolkit/apierror"
	"go.goms.io/aks/rp/toolkit/armerror"
	"go.goms.io/aks/rp/toolkit/azureclients/loadbalancer/mock_loadbalancer"
	"go.goms.io/aks/rp/toolkit/azureclients/publicipaddress/mock_publicipaddress"
	cgerror "go.goms.io/aks/rp/toolkit/categorizederror"
	"go.goms.io/aks/rp/toolkit/log"
	"go.goms.io/aks/rp/toolkit/testlib"
)

var _ = Describe("standard load balancer reconciler", func() {
	var (
		mockCtrl *gomock.Controller
		ctx      context.Context
		logger   *log.Logger

		location              string
		computeSubscriptionID string
		networkSubscriptionID string

		nodeResourceGroupName                    string
		clusterResourceGroupName                 string
		clusterName                              string
		slbName                                  string
		slbOutboundRuleName                      string
		slbOutboundBackendPoolName               string
		slbOutboundRuleAllocatedOutboundPorts    int32
		slbOutboundRuleIdleTimeoutInMinutes      int32
		slbOutboundRuleEnableTCPReset            bool
		slbOutboundRuleProtocol                  string
		slbManagedOutboundIPTagName              string
		slbManagedOutboundIPTagValue             string
		slbManagedOutboundIPTags                 map[string]*string
		nonPrefixedSlbManagedOutboundIPTags      map[string]*string
		slbManagedOutboundIPCount                int
		slbManagedOutboundIPIdleTimeoutInMinutes int32
		toggle                                   *fake.Toggles

		lbClient              *mock_loadbalancer.MockInterface
		vmssReconciler        *mock_agentpool.MockAgentPoolLBBackendpoolReconciler
		vmReconciler          *mock_agentpool.MockAgentPoolLBBackendpoolReconciler
		vmReconcilerTrack2    *mock_agentpool.MockAgentPoolLBBackendpoolReconciler
		pipClient             *mock_publicipaddress.MockInterface
		pipClientTrack2       *mock_azureresources.MockPublicIPAddressInterface
		goal                  *goalresolvers.OutboundGoal
		outboundConn          *slbOutBoundReconciler
		existingLBInGoalState *network.LoadBalancer
		goalIPNames           []string
		goalIPIDs             []string
		goalIPs               map[string]adapters.PublicIPAddress
		goalIPConfigIDs       []string

		goalIPPrefixNames     []string
		goalIPPrefixIDs       []string
		goalIPPrefixes        map[string]adapters.PublicIPPrefix
		goalIPPrefixConfigIDs []string

		agentPoolsRetriever *mock_rawgoalretriever.MockAgentPoolsInterface
	)

	BeforeEach(func() {
		mockCtrl = gomock.NewController(GinkgoT())
		lbClient = mock_loadbalancer.NewMockInterface(mockCtrl)
		vmssReconciler = mock_agentpool.NewMockAgentPoolLBBackendpoolReconciler(mockCtrl)
		vmReconciler = mock_agentpool.NewMockAgentPoolLBBackendpoolReconciler(mockCtrl)
		vmReconcilerTrack2 = mock_agentpool.NewMockAgentPoolLBBackendpoolReconciler(mockCtrl)
		pipClient = mock_publicipaddress.NewMockInterface(mockCtrl)
		pipClientTrack2 = mock_azureresources.NewMockPublicIPAddressInterface(mockCtrl)
		logger = log.InitializeTestLogger()
		ctx = log.WithLogger(context.Background(), logger)
		apiTracking := log.NewAPITrackingFromParametersMap(nil)
		ctx = log.WithAPITracking(ctx, apiTracking)

		location = "westus"
		computeSubscriptionID = "b8c5d784-96a1-4e65-9acb-9246b26c8888"
		networkSubscriptionID = "b8c5d784-96a1-4e65-9acb-9246b26c8888"
		nodeResourceGroupName = "mc_testingrg_testcluster"
		clusterResourceGroupName = "testingrg_testcluster"
		clusterName = "testcluster"
		slbName = consts.SLBName
		slbOutboundRuleName = "aksOutboundRule"
		slbOutboundBackendPoolName = "aksOutboundBackendPool"
		slbOutboundRuleAllocatedOutboundPorts = 0
		slbOutboundRuleIdleTimeoutInMinutes = 30
		slbOutboundRuleEnableTCPReset = true
		slbOutboundRuleProtocol = "All"
		slbManagedOutboundIPTagName = consts.PrefixedSLBManagedOutboundIPTypeTagName
		slbManagedOutboundIPTagValue = consts.SLBManagedOutboundIPTypeTagValue
		slbManagedOutboundIPTags = make(map[string]*string)
		slbManagedOutboundIPTags[slbManagedOutboundIPTagName] = &slbManagedOutboundIPTagValue
		nonPrefixedSlbManagedOutboundIPTags = make(map[string]*string)
		nonPrefixedSlbManagedOutboundIPTags[consts.SLBManagedOutboundIPTypeTagName] = &slbManagedOutboundIPTagValue
		nonPrefixedSlbManagedOutboundIPTags["owner"] = &slbName
		slbManagedOutboundIPCount = 2
		slbManagedOutboundIPIdleTimeoutInMinutes = 30
		toggle = fake.New()
		flag := flags.NewFlags(toggle, &rollout.Entity{})
		agentPoolsRetriever = mock_rawgoalretriever.NewMockAgentPoolsInterface(mockCtrl)
		outboundConn = &slbOutBoundReconciler{
			vmssReconciler:       vmssReconciler,
			vmReconciler:         vmReconciler,
			vmReconcilerTrack2:   vmReconcilerTrack2,
			loadbalancerClient:   lbClient,
			publicIPClient:       pipClient,
			publicIPClientTrack2: pipClientTrack2,
			opt:                  flag,
			agentPoolsRetriever:  agentPoolsRetriever,
		}

		idx := 0

		//Public IP(managed+customer)
		goalIPNames = []string{"mgdip1", "mgdip2", "customerip1", "customerip2"}
		goalIPIDs = make([]string, 0, len(goalIPNames))
		goalIPConfigNames := make([]string, 0, len(goalIPNames))
		goalIPConfigIDs = make([]string, 0, len(goalIPNames))
		goalIPConfigs := make([]network.FrontendIPConfiguration, 0, len(goalIPNames))
		goalIPConfigRefs := make([]network.SubResource, 0, len(goalIPNames))
		goalIPs = make(map[string]adapters.PublicIPAddress)

		for _, ipName := range goalIPNames {
			idx++

			goalIPID := fmt.Sprintf("/subscriptions/%s/resourcegroups/%s/providers/microsoft.network/publicipaddresses/%s",
				computeSubscriptionID,
				nodeResourceGroupName,
				ipName)

			goalIPIDs = append(goalIPIDs, goalIPID)

			goalIPConfigName := fmt.Sprintf("%d-%s", idx, ipName)

			goalIPConfigNames = append(goalIPConfigNames, goalIPConfigName)

			goalIPConfigID := GetSLBFrontendIPConfigID(
				computeSubscriptionID,
				nodeResourceGroupName,
				slbName,
				goalIPConfigName)

			goalIPConfigIDs = append(goalIPConfigIDs, goalIPConfigID)

			goalIPConfigs = append(goalIPConfigs,
				network.FrontendIPConfiguration{
					Name: to.StringPtr(goalIPConfigName),
					ID:   to.StringPtr(goalIPConfigID),
					FrontendIPConfigurationPropertiesFormat: &network.FrontendIPConfigurationPropertiesFormat{
						PublicIPAddress: &network.PublicIPAddress{
							ID: to.StringPtr(goalIPID),
						},
					},
				})

			goalIPConfigRefs = append(goalIPConfigRefs,
				network.SubResource{
					ID: to.StringPtr(goalIPConfigID),
				})

			publicIPAddress := fmt.Sprintf("%d.%d.%d.%d", idx, idx, idx, idx)
			goalIP := adapters.NewTrack1PublicIPAddressAdapter(&network.PublicIPAddress{
				Name:     to.StringPtr(ipName),
				ID:       to.StringPtr(goalIPID),
				Location: &location,
				Sku: &network.PublicIPAddressSku{
					Name: "Standard",
				},
				Tags: map[string]*string{
					slbManagedOutboundIPTagName: &slbManagedOutboundIPTagValue,
					"owner":                     &slbName,
					tags.ClusterName:            &clusterName,
					tags.ClusterRG:              &clusterResourceGroupName,
				},
				PublicIPAddressPropertiesFormat: &network.PublicIPAddressPropertiesFormat{
					PublicIPAllocationMethod: network.Static,
					IdleTimeoutInMinutes:     to.Int32Ptr(30),
					ProvisioningState:        network.ProvisioningStateSucceeded,
					IPAddress:                to.StringPtr(publicIPAddress),
				},
			})

			if !strings.HasPrefix(ipName, "mgd") {
				goalIP.GetTags()[slbManagedOutboundIPTagName] = nil
			}

			goalIPs[strings.ToLower(goalIPID)] = goalIP
		}

		//Public IP Prefix
		goalIPPrefixNames = []string{"prefix1", "prefix2"}
		goalIPPrefixIDs = make([]string, 0, len(goalIPConfigNames))
		goalIPPrefixConfigIDs = make([]string, 0, len(goalIPPrefixNames))
		goalIPPrefixConfigs := make([]network.FrontendIPConfiguration, 0, len(goalIPPrefixNames))
		goalIPPrefixConfigRefs := make([]network.SubResource, 0, len(goalIPPrefixNames))
		goalIPPrefixes = make(map[string]adapters.PublicIPPrefix)

		for _, ipPrefixName := range goalIPPrefixNames {
			idx++

			goalIPPrefixID := fmt.Sprintf("/subscriptions/%s/resourcegroups/%s/providers/microsoft.network/publicipprefixes/%s",
				computeSubscriptionID,
				nodeResourceGroupName,
				ipPrefixName)

			goalIPPrefixIDs = append(goalIPPrefixIDs, goalIPPrefixID)

			goalIPPrefixConfigName := fmt.Sprintf("%d-%s", idx, ipPrefixName)

			goalIPPrefixConfigID := GetSLBFrontendIPConfigID(
				computeSubscriptionID,
				nodeResourceGroupName,
				slbName,
				goalIPPrefixConfigName)

			goalIPPrefixConfigIDs = append(goalIPPrefixConfigIDs, goalIPPrefixConfigID)

			goalIPPrefixConfigs = append(goalIPPrefixConfigs,
				network.FrontendIPConfiguration{
					Name: to.StringPtr(goalIPPrefixConfigName),
					ID:   to.StringPtr(goalIPPrefixConfigID),
					FrontendIPConfigurationPropertiesFormat: &network.FrontendIPConfigurationPropertiesFormat{
						PublicIPPrefix: &network.SubResource{
							ID: to.StringPtr(goalIPPrefixID),
						},
					},
				})

			goalIPPrefixConfigRefs = append(goalIPPrefixConfigRefs,
				network.SubResource{
					ID: to.StringPtr(goalIPPrefixConfigID),
				})

			publicIPPrefix := fmt.Sprintf("%d.%d.%d.%d/%d", idx, idx, idx, idx, idx)
			goalIPPrefix := adapters.NewTrack1PublicIPPrefixAdapter(&network.PublicIPPrefix{
				Name:     to.StringPtr(ipPrefixName),
				ID:       to.StringPtr(goalIPPrefixID),
				Location: &location,
				Sku: &network.PublicIPPrefixSku{
					Name: "Standard",
				},
				PublicIPPrefixPropertiesFormat: &network.PublicIPPrefixPropertiesFormat{
					PrefixLength:           to.Int32Ptr(29),
					IPPrefix:               to.StringPtr(publicIPPrefix),
					ProvisioningState:      network.ProvisioningStateSucceeded,
					PublicIPAddressVersion: "IPv4",
				},
				Tags: map[string]*string{
					slbManagedOutboundIPTagName: &slbManagedOutboundIPTagValue,
					"owner":                     &slbName,
					tags.ClusterName:            &clusterName,
					tags.ClusterRG:              &clusterResourceGroupName,
				},
			})
			goalIPPrefixes[strings.ToLower(goalIPPrefixID)] = goalIPPrefix
		}

		allIPConfigs := append(goalIPConfigs, goalIPPrefixConfigs...)
		allIPConfigRefs := append(goalIPConfigRefs, goalIPPrefixConfigRefs...)

		goalOutboundPoolID := utils.GetLBBackEndPoolID(
			computeSubscriptionID,
			nodeResourceGroupName,
			slbName,
			slbOutboundBackendPoolName)

		goalSLBID := fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s",
			computeSubscriptionID,
			nodeResourceGroupName,
			slbName)

		goal = &goalresolvers.OutboundGoal{
			ManagedClusterSubscriptionID:    computeSubscriptionID,
			NetworkSubscriptionID:           computeSubscriptionID,
			NodeResourceGroupName:           nodeResourceGroupName,
			Location:                        location,
			ManagedClusterName:              clusterName,
			ManagedClusterResourceGroupName: clusterResourceGroupName,
			EnableIPv6:                      true,
			ManagedSLBGoal: &goalresolvers.ManagedSLBGoal{
				OutboundRuleAllocatedOutboundPorts: slbOutboundRuleAllocatedOutboundPorts,
				OutboundRuleIdleTimeoutInMinutes:   slbOutboundRuleIdleTimeoutInMinutes,
				ManagedOutboundIPs: &goalresolvers.ManagedOutboundIPsGoal{
					Count:                slbManagedOutboundIPCount,
					IdleTimeoutInMinutes: slbManagedOutboundIPIdleTimeoutInMinutes,
				},
				OutboundIPPrefixes: &goalresolvers.OutboundIPPrefixesGoal{
					IDs: goalIPPrefixIDs,
				},
				OutboundIPs: &goalresolvers.OutboundIPsGoal{
					IDs: goalIPIDs[slbManagedOutboundIPCount:],
				},
				BackendPoolType: rpcommonconsts.LBBackendPoolTypeNodeIPConfiguration,
			},
		}

		existingLBInGoalState = &network.LoadBalancer{
			Name:     &slbName,
			ID:       &goalSLBID,
			Location: &location,
			Sku: &network.LoadBalancerSku{
				Name: "Standard",
			},
			LoadBalancerPropertiesFormat: &network.LoadBalancerPropertiesFormat{
				FrontendIPConfigurations: &allIPConfigs,
				OutboundRules: &[]network.OutboundRule{
					{
						Name: to.StringPtr(slbOutboundRuleName),
						OutboundRulePropertiesFormat: &network.OutboundRulePropertiesFormat{
							FrontendIPConfigurations: &allIPConfigRefs,
							AllocatedOutboundPorts:   to.Int32Ptr(slbOutboundRuleAllocatedOutboundPorts),
							IdleTimeoutInMinutes:     to.Int32Ptr(slbOutboundRuleIdleTimeoutInMinutes),
							EnableTCPReset:           to.BoolPtr(slbOutboundRuleEnableTCPReset),
							Protocol:                 network.LoadBalancerOutboundRuleProtocol(slbOutboundRuleProtocol),
							BackendAddressPool: &network.SubResource{
								ID: &goalOutboundPoolID,
							},
							ProvisioningState: network.ProvisioningStateSucceeded,
						},
					},
				},
				BackendAddressPools: &[]network.BackendAddressPool{
					{
						Name: to.StringPtr(consts.SlbOutboundBackendPoolName),
						ID:   to.StringPtr(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolName)),
					},
					{
						Name: to.StringPtr(consts.SlbInboundBackendPoolName),
						ID:   to.StringPtr(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, consts.SlbInboundBackendPoolName)),
					},
					{
						Name: to.StringPtr(consts.SlbOutboundBackendPoolNameIPv6),
						ID:   to.StringPtr(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolNameIPv6)),
					},
					{
						Name: to.StringPtr(consts.SlbInboundBackendPoolNameIPv6),
						ID:   to.StringPtr(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, consts.SlbInboundBackendPoolNameIPv6)),
					},
				},
				ProvisioningState: network.ProvisioningStateSucceeded,
			},
		}
	})

	AfterEach(func() {
		mockCtrl.Finish()
	})
	azureresources_test.DescribeTrack2ToggleMatrix(&toggle, "enable-sdk-track2-publicipaddresses", func(enableTrack2PIP bool) {
		azureresources_test.DescribeTrack2ToggleMatrix(&toggle, "enable-sdk-track2-slb-outbound-reconciler", func(enableTrack2VM bool) {
			Context("EnsureOutboundResources", func() {
				When("goal load balancer does not exist", func() {
					It("should ensure load balancer", func() {
						if enableTrack2PIP {
							pipClientTrack2.EXPECT().Get(testlib.IsContext(), gomock.Any(), gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientGetResponse{}, &cgerror.CategorizedError{HTTPStatus: to.IntPtr(http.StatusNotFound)})
							pipClientTrack2.EXPECT().CreateOrUpdate(testlib.IsContext(), nodeResourceGroupName, SLBTemporaryPIPName, gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientCreateOrUpdateResponse{
								PublicIPAddress: armnetwork.PublicIPAddress{Name: to.StringPtr(SLBTemporaryPIPName)},
							}, nil).Times(1)
						} else {
							pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
							pipClient.EXPECT().CreateOrUpdatePublicIPAddress(gomock.Any(), networkSubscriptionID, nodeResourceGroupName, SLBTemporaryPIPName, gomock.Any()).Return(&network.PublicIPAddress{
								Name: to.StringPtr(SLBTemporaryPIPName),
							}, nil).Times(1)
						}

						lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
						lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), networkSubscriptionID, nodeResourceGroupName, slbName, gomock.Any()).Return(existingLBInGoalState, nil).Times(1)
						agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return([]*hcpAgentPool.AgentPoolResource{}, nil).Times(1)
						if enableTrack2VM {
							vmReconcilerTrack2.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						} else {
							vmReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						}
						vmssReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
							func(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
								Expect(len(backendpoolIDs)).To(Equal(2))
								Expect(len(backendpoolIDsIPV6)).To(Equal(2))
								Expect(len(excludedAgentPoolNames)).To(Equal(0))
								return nil
							}).Times(1)
						err := outboundConn.EnsureOutboundResources(ctx, goal, nil, nil)

						Expect(err).To(BeNil())
					})
				})
				When("goal load balancer does not exist", func() {
					It("should ensure load balancer", func() {
						goal.ManagedSLBGoal.BackendPoolType = rpcommonconsts.LBBackendPoolTypeNodeIP
						if enableTrack2PIP {
							pipClientTrack2.EXPECT().Get(testlib.IsContext(), gomock.Any(), gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientGetResponse{}, &cgerror.CategorizedError{HTTPStatus: to.IntPtr(http.StatusNotFound)})
							pipClientTrack2.EXPECT().CreateOrUpdate(testlib.IsContext(), nodeResourceGroupName, SLBTemporaryPIPName, gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientCreateOrUpdateResponse{
								PublicIPAddress: armnetwork.PublicIPAddress{Name: to.StringPtr(SLBTemporaryPIPName)},
							}, nil).Times(1)
						} else {
							pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
							pipClient.EXPECT().CreateOrUpdatePublicIPAddress(gomock.Any(), networkSubscriptionID, nodeResourceGroupName, SLBTemporaryPIPName, gomock.Any()).Return(&network.PublicIPAddress{
								Name: to.StringPtr(SLBTemporaryPIPName),
							}, nil).Times(1)
						}

						lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
						lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), networkSubscriptionID, nodeResourceGroupName, slbName, gomock.Any()).Return(existingLBInGoalState, nil).Times(1)
						agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return([]*hcpAgentPool.AgentPoolResource{}, nil).Times(1)
						if enableTrack2VM {
							vmReconcilerTrack2.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						} else {
							vmReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						}
						vmssReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
							func(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
								Expect(len(backendpoolIDs)).To(Equal(1))
								Expect(len(backendpoolIDsIPV6)).To(Equal(1))
								return nil
							}).Times(1)
						err := outboundConn.EnsureOutboundResources(ctx, goal, nil, nil)

						Expect(err).To(BeNil())
					})
					It("should return error if CreateOrUpdateLoadBalancer failed", func() {
						goal.ManagedSLBGoal.BackendPoolType = rpcommonconsts.LBBackendPoolTypeNodeIP
						if enableTrack2PIP {
							pipClientTrack2.EXPECT().Get(testlib.IsContext(), gomock.Any(), gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientGetResponse{}, &cgerror.CategorizedError{HTTPStatus: to.IntPtr(http.StatusNotFound)})
							pipClientTrack2.EXPECT().CreateOrUpdate(testlib.IsContext(), nodeResourceGroupName, SLBTemporaryPIPName, gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientCreateOrUpdateResponse{
								PublicIPAddress: armnetwork.PublicIPAddress{Name: to.StringPtr(SLBTemporaryPIPName)},
							}, nil).Times(1)
						} else {
							pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
							pipClient.EXPECT().CreateOrUpdatePublicIPAddress(gomock.Any(), networkSubscriptionID, nodeResourceGroupName, SLBTemporaryPIPName, gomock.Any()).Return(&network.PublicIPAddress{
								Name: to.StringPtr(SLBTemporaryPIPName),
							}, nil).Times(1)
						}

						lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
						lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), networkSubscriptionID, nodeResourceGroupName, slbName, gomock.Any()).Return(nil, &cgerror.CategorizedError{
							SubCode: cgerror.ErrorSubCode("SubscriptionWarned"),
						}).Times(1)

						err := outboundConn.EnsureOutboundResources(ctx, goal, nil, nil)
						Expect(err).NotTo(BeNil())
						Expect(err.Code).To(Equal(hcpEnums.ErrorCode_CreateOrUpdateLoadBalancerError))
						Expect(err.SubCode).To(Equal(cgerror.ErrorSubCode("SubscriptionWarned")))
					})
				})
				When("failed to reconcile agentpool", func() {
					It("should ensure load balancer", func() {
						if enableTrack2PIP {
							pipClientTrack2.EXPECT().Get(testlib.IsContext(), gomock.Any(), gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientGetResponse{}, &cgerror.CategorizedError{HTTPStatus: to.IntPtr(http.StatusNotFound)})
							pipClientTrack2.EXPECT().CreateOrUpdate(testlib.IsContext(), nodeResourceGroupName, SLBTemporaryPIPName, gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientCreateOrUpdateResponse{
								PublicIPAddress: armnetwork.PublicIPAddress{Name: to.StringPtr(SLBTemporaryPIPName)},
							}, nil).Times(1)
						} else {
							pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
							pipClient.EXPECT().CreateOrUpdatePublicIPAddress(gomock.Any(), networkSubscriptionID, nodeResourceGroupName, SLBTemporaryPIPName, gomock.Any()).Return(&network.PublicIPAddress{
								Name: to.StringPtr(SLBTemporaryPIPName),
							}, nil).Times(1)
						}

						lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
						lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), networkSubscriptionID, nodeResourceGroupName, slbName, gomock.Any()).Return(existingLBInGoalState, nil).Times(1)
						agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return([]*hcpAgentPool.AgentPoolResource{}, nil).Times(1)
						vmssReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						if enableTrack2VM {
							vmReconcilerTrack2.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(cgerror.CreateInternalError()).Times(1)
						} else {
							vmReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(cgerror.CreateInternalError()).Times(1)
						}
						err := outboundConn.EnsureOutboundResources(ctx, goal, nil, nil)

						Expect(err).NotTo(BeNil())
					})
				})
				When("goal load balancer exists", func() {
					It("should ensure load balancer", func() {
						lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(existingLBInGoalState, nil)
						agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return([]*hcpAgentPool.AgentPoolResource{}, nil).Times(1)
						vmssReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).DoAndReturn(
							func(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
								Expect(len(backendpoolIDs)).To(Equal(2))
								Expect(len(backendpoolIDsIPV6)).To(Equal(2))
								Expect(len(excludedAgentPoolNames)).To(Equal(0))
								return nil
							}).Times(1)
						if enableTrack2VM {
							vmReconcilerTrack2.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						} else {
							vmReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						}
						err := outboundConn.EnsureOutboundResources(ctx, goal, nil, nil)

						Expect(err).To(BeNil())
					})

					It("should not attaching NICs to the backend pool if the backend pool is IP-based", func() {
						(*existingLBInGoalState.BackendAddressPools)[1] = network.BackendAddressPool{
							Name: to.StringPtr(consts.SlbInboundBackendPoolName),
							ID:   to.StringPtr(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, consts.SlbInboundBackendPoolName)),
							BackendAddressPoolPropertiesFormat: &network.BackendAddressPoolPropertiesFormat{
								LoadBalancerBackendAddresses: &[]network.LoadBalancerBackendAddress{
									{
										LoadBalancerBackendAddressPropertiesFormat: &network.LoadBalancerBackendAddressPropertiesFormat{
											IPAddress: to.StringPtr("*******"),
										},
									},
								},
							},
						}
						lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(existingLBInGoalState, nil)
						agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return([]*hcpAgentPool.AgentPoolResource{}, nil).Times(1)
						vmssReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).DoAndReturn(
							func(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
								Expect(len(backendpoolIDs)).To(Equal(1))
								Expect(len(backendpoolIDsIPV6)).To(Equal(2))
								return nil
							}).Times(1)
						if enableTrack2VM {
							vmReconcilerTrack2.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						} else {
							vmReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						}
						err := outboundConn.EnsureOutboundResources(ctx, goal, nil, nil)

						Expect(err).To(BeNil())
					})
				})
				When("get loadbalancer failed", func() {
					It("should report an error ", func() {
						slbID := fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s",
							computeSubscriptionID,
							nodeResourceGroupName,
							slbName)

						slb := &network.LoadBalancer{
							Name:     &slbName,
							ID:       &slbID,
							Location: &location,
							Sku: &network.LoadBalancerSku{
								Name: "Basic",
							},
							LoadBalancerPropertiesFormat: &network.LoadBalancerPropertiesFormat{
								ProvisioningState: network.ProvisioningStateSucceeded,
							},
						}

						lbClient.EXPECT().GetLoadBalancer(gomock.Any(), computeSubscriptionID, nodeResourceGroupName, slbName).Return(slb, nil).Times(1)

						cerr := outboundConn.EnsureOutboundResources(ctx, goal, nil, nil)

						Expect(cerr).NotTo(BeNil())
						Expect(cerr.SubCode).To(Equal(cgerror.NotStandardLoadBalancer))
					})
				})
				When("goal has extendedLocation set", func() {
					It("should ensure load balancer", func() {
						exLocName := "microsoftlosangeles1"
						exLocType := "edgeZone"
						goal.ExtendedLocation = &goalresolvers.ExtendedLocation{
							Name: exLocName,
							Type: exLocType,
						}
						lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
						ipName := "mgdip1"

						goalIPID := fmt.Sprintf("/subscriptions/%s/resourcegroups/%s/providers/microsoft.network/publicipaddresses/%s", computeSubscriptionID, nodeResourceGroupName, ipName)
						publicIPAddress := fmt.Sprintf("%d.%d.%d.%d", 1, 1, 1, 1)
						pipWithExtendedLocation := adapters.NewTrack1PublicIPAddressAdapter(&network.PublicIPAddress{
							Name:     to.StringPtr(ipName),
							ID:       to.StringPtr(goalIPID),
							Location: &location,
							Sku: &network.PublicIPAddressSku{
								Name: "Standard",
							},
							Tags: map[string]*string{
								slbManagedOutboundIPTagName: &slbManagedOutboundIPTagValue,
								"owner":                     &slbName,
								tags.ClusterName:            &clusterName,
								tags.ClusterRG:              &clusterResourceGroupName,
							},
							PublicIPAddressPropertiesFormat: &network.PublicIPAddressPropertiesFormat{
								PublicIPAllocationMethod: network.Static,
								IdleTimeoutInMinutes:     to.Int32Ptr(30),
								ProvisioningState:        network.ProvisioningStateSucceeded,
								IPAddress:                to.StringPtr(publicIPAddress),
							},
						})
						outboundIPs := make(map[string]adapters.PublicIPAddress)

						outboundIPs[strings.ToLower(goalIPID)] = pipWithExtendedLocation

						lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), computeSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any()).DoAndReturn(
							func(
								ctx context.Context,
								subscriptionID, resourceGroupName, loadBalancerName string,
								parameters *network.LoadBalancer) (*network.LoadBalancer, *cgerror.CategorizedError) {
								Expect(*parameters.ExtendedLocation.Name).To(Equal(exLocName))
								Expect(parameters.ExtendedLocation.Type).To(Equal(network.ExtendedLocationTypes(exLocType)))
								return existingLBInGoalState, nil
							})

						if enableTrack2PIP {
							pipClientTrack2.EXPECT().Delete(testlib.IsContext(), gomock.Any(), gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientDeleteResponse{}, nil)
						} else {
							pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), computeSubscriptionID, nodeResourceGroupName, SLBTemporaryPIPName).Return(nil, nil).Times(1)
						}
						agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return([]*hcpAgentPool.AgentPoolResource{}, nil).Times(1)
						vmssReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						if enableTrack2VM {
							vmReconcilerTrack2.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						} else {
							vmReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						}
						cerr := outboundConn.EnsureOutboundResources(ctx, goal, outboundIPs, nil)

						Expect(cerr).To(BeNil())
					})
				})
			})
			When("goal has ExtraResourceTags which conflicts with the tags of existing lb", func() {
				It("should ensure load balancer successfully and overwrite the tags with the goal.ExtraResourceTags", func() {
					goal.ExtraResourceTags = map[string]*string{
						"extraTag": to.StringPtr("newvalue"),
					}

					slbID := fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s",
						computeSubscriptionID,
						nodeResourceGroupName,
						slbName)
					slb := &network.LoadBalancer{
						Name:     &slbName,
						ID:       &slbID,
						Location: &location,
						Sku: &network.LoadBalancerSku{
							Name: "Standard",
						},
						Tags: map[string]*string{
							tags.ClusterName: &clusterName,
							tags.ClusterRG:   &clusterResourceGroupName,
							"extraTag":       to.StringPtr("oldvalue"),
						},
						LoadBalancerPropertiesFormat: &network.LoadBalancerPropertiesFormat{
							ProvisioningState: network.ProvisioningStateSucceeded,
						},
					}

					lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(slb, nil)
					ipName := "mgdip1"

					goalIPID := fmt.Sprintf("/subscriptions/%s/resourcegroups/%s/providers/microsoft.network/publicipaddresses/%s", computeSubscriptionID, nodeResourceGroupName, ipName)
					publicIPAddress := fmt.Sprintf("%d.%d.%d.%d", 1, 1, 1, 1)
					pipWithExtendedLocation := adapters.NewTrack1PublicIPAddressAdapter(&network.PublicIPAddress{
						Name:     to.StringPtr(ipName),
						ID:       to.StringPtr(goalIPID),
						Location: &location,
						Sku: &network.PublicIPAddressSku{
							Name: "Standard",
						},
						Tags: map[string]*string{
							slbManagedOutboundIPTagName: &slbManagedOutboundIPTagValue,
							"owner":                     &slbName,
							tags.ClusterName:            &clusterName,
							tags.ClusterRG:              &clusterResourceGroupName,
						},
						PublicIPAddressPropertiesFormat: &network.PublicIPAddressPropertiesFormat{
							PublicIPAllocationMethod: network.Static,
							IdleTimeoutInMinutes:     to.Int32Ptr(30),
							ProvisioningState:        network.ProvisioningStateSucceeded,
							IPAddress:                to.StringPtr(publicIPAddress),
						},
					})
					outboundIPs := make(map[string]adapters.PublicIPAddress)

					outboundIPs[strings.ToLower(goalIPID)] = pipWithExtendedLocation

					existingLBInGoalState.Tags = map[string]*string{
						tags.ClusterName: &clusterName,
						tags.ClusterRG:   &clusterResourceGroupName,
						"extraTag":       to.StringPtr("newvalue"),
					}
					lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), computeSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any()).DoAndReturn(
						func(
							ctx context.Context,
							subscriptionID, resourceGroupName, loadBalancerName string,
							parameters *network.LoadBalancer) (*network.LoadBalancer, *cgerror.CategorizedError) {
							Expect(*parameters.Tags["extraTag"]).To(Equal("newvalue"))
							return existingLBInGoalState, nil
						})

					if enableTrack2PIP {
						pipClientTrack2.EXPECT().Delete(testlib.IsContext(), nodeResourceGroupName, SLBTemporaryPIPName, nil).Return(armnetwork.PublicIPAddressesClientDeleteResponse{}, nil)
					} else {
						pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), computeSubscriptionID, nodeResourceGroupName, SLBTemporaryPIPName).Return(nil, nil).Times(1)
					}
					agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return([]*hcpAgentPool.AgentPoolResource{}, nil).Times(1)
					vmssReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
					if enableTrack2VM {
						vmReconcilerTrack2.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
					} else {
						vmReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
					}
					cerr := outboundConn.EnsureOutboundResources(ctx, goal, outboundIPs, nil)

					Expect(cerr).To(BeNil())
				})
			})
			Context("DeleteOutboundResources", func() {
				When("goal load balancer exists", func() {
					It("should delete load balancer", func() {
						existingLBInGoalState.LoadBalancerPropertiesFormat.LoadBalancingRules = nil
						if enableTrack2PIP {
							pipClientTrack2.EXPECT().Delete(testlib.IsContext(), gomock.Any(), gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientDeleteResponse{}, nil)
						} else {
							pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
						}

						lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(existingLBInGoalState, nil)
						lbClient.EXPECT().DeleteLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
						if enableTrack2VM {
							vmReconcilerTrack2.EXPECT().DecoupleLBBackendPool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any()).Return(nil).Times(1)
						} else {
							vmReconciler.EXPECT().DecoupleLBBackendPool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any()).Return(nil).Times(1)
						}
						vmssReconciler.EXPECT().DecoupleLBBackendPool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any()).DoAndReturn(
							func(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}) *cgerror.CategorizedError {
								Expect(len(backendpoolIDs)).To(Equal(4))
								Expect(len(backendpoolIDsIPV6)).To(Equal(4))
								return nil
							}).Times(1)
						err := outboundConn.DeleteOutboundResources(ctx, goal)

						Expect(err).To(BeNil())
					})
				})
				When("failed to reconcile agent pool", func() {
					It("should return failure", func() {
						existingLBInGoalState.LoadBalancerPropertiesFormat.LoadBalancingRules = nil

						lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(existingLBInGoalState, nil)
						if enableTrack2VM {
							vmReconcilerTrack2.EXPECT().DecoupleLBBackendPool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any()).Return(cgerror.CreateInternalError()).Times(1)
						} else {
							vmReconciler.EXPECT().DecoupleLBBackendPool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any()).Return(cgerror.CreateInternalError()).Times(1)
						}
						err := outboundConn.DeleteOutboundResources(ctx, goal)

						Expect(err).NotTo(BeNil())
					})
				})
				When("goal load balancer exists", func() {
					It("should delete outbound config", func() {
						if enableTrack2PIP {
							pipClientTrack2.EXPECT().Delete(testlib.IsContext(), gomock.Any(), gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientDeleteResponse{}, nil)
						} else {
							pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
						}

						existingLBInGoalState.LoadBalancingRules = &[]network.LoadBalancingRule{
							{
								Name: to.StringPtr("rule1"),
								LoadBalancingRulePropertiesFormat: &network.LoadBalancingRulePropertiesFormat{
									BackendAddressPool: &network.SubResource{
										ID: to.StringPtr(utils.GetLBBackEndPoolID(computeSubscriptionID, nodeResourceGroupName, slbName, consts.SlbInboundBackendPoolName)),
									},
								},
							},
						}
						(*existingLBInGoalState.FrontendIPConfigurations)[0].LoadBalancingRules = &[]network.SubResource{
							{
								ID: to.StringPtr(fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/loadBalancingRules/%s", computeSubscriptionID, nodeResourceGroupName, slbName, "rule1")),
							},
						}
						lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(existingLBInGoalState, nil)
						lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(existingLBInGoalState, nil).Times(1)
						vmssReconciler.EXPECT().DecoupleLBBackendPool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any()).Return(nil).Times(1)
						if enableTrack2VM {
							vmReconcilerTrack2.EXPECT().DecoupleLBBackendPool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any()).Return(nil).Times(1)
						} else {
							vmReconciler.EXPECT().DecoupleLBBackendPool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any()).Return(nil).Times(1)
						}
						err := outboundConn.DeleteOutboundResources(ctx, goal)

						Expect(err).To(BeNil())
					})
				})
			})
		})
		Context("CleanUpFrontEndIPConfigs", func() {
			When("goal load balancer exists", func() {
				It("should delete outbound config", func() {
					if enableTrack2PIP {
						pipClientTrack2.EXPECT().Delete(testlib.IsContext(), gomock.Any(), gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientDeleteResponse{}, nil)
					} else {
						pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
					}

					existingLBInGoalState.LoadBalancingRules = &[]network.LoadBalancingRule{
						{
							Name: to.StringPtr("rule1"),
							LoadBalancingRulePropertiesFormat: &network.LoadBalancingRulePropertiesFormat{
								BackendAddressPool: &network.SubResource{
									ID: to.StringPtr(utils.GetLBBackEndPoolID(computeSubscriptionID, nodeResourceGroupName, slbName, consts.SlbInboundBackendPoolName)),
								},
							},
						},
					}
					(*existingLBInGoalState.FrontendIPConfigurations)[0].LoadBalancingRules = &[]network.SubResource{
						{
							ID: to.StringPtr(fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/loadBalancingRules/%s", computeSubscriptionID, nodeResourceGroupName, slbName, "rule1")),
						},
					}

					lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(existingLBInGoalState, nil)
					lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(existingLBInGoalState, nil).Times(1)
					err := outboundConn.CleanUpFrontEndIPConfigs(ctx, goal)

					Expect(err).To(BeNil())
				})
			})
			When("goal load balancer exists", func() {
				It("should delete outbound config", func() {
					if enableTrack2PIP {
						pipClientTrack2.EXPECT().Delete(testlib.IsContext(), gomock.Any(), gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientDeleteResponse{}, nil)
					} else {
						pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
					}

					existingLBInGoalState.LoadBalancingRules = &[]network.LoadBalancingRule{
						{
							Name: to.StringPtr("rule1"),
							LoadBalancingRulePropertiesFormat: &network.LoadBalancingRulePropertiesFormat{
								BackendAddressPool: &network.SubResource{
									ID: to.StringPtr(utils.GetLBBackEndPoolID(computeSubscriptionID, nodeResourceGroupName, slbName, consts.SlbInboundBackendPoolName)),
								},
							},
						},
					}
					(*existingLBInGoalState.FrontendIPConfigurations)[0].LoadBalancingRules = &[]network.SubResource{
						{
							ID: to.StringPtr(fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/loadBalancingRules/%s", computeSubscriptionID, nodeResourceGroupName, slbName, "rule1")),
						},
					}
					(*existingLBInGoalState.FrontendIPConfigurations)[1].OutboundRules = &[]network.SubResource{
						{
							ID: to.StringPtr(fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/outboundRules/%s", computeSubscriptionID, nodeResourceGroupName, slbName, slbOutboundRuleName)),
						},
					}
					lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(existingLBInGoalState, nil)
					lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(existingLBInGoalState, nil).Times(1)
					err := outboundConn.CleanUpFrontEndIPConfigs(ctx, goal)

					Expect(err).To(BeNil())
				})
			})
		})
		Context("AssociateFrontEndIPConfigs", func() {
			When("goal load balancer exists", func() {
				It("should create slb", func() {
					if enableTrack2PIP {
						pipClientTrack2.EXPECT().Delete(testlib.IsContext(), gomock.Any(), gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientDeleteResponse{}, nil)
					} else {
						pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
					}

					(*existingLBInGoalState.FrontendIPConfigurations)[0].LoadBalancingRules = &[]network.SubResource{
						{
							ID: to.StringPtr(fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/loadBalancingRules/%s", computeSubscriptionID, nodeResourceGroupName, slbName, "rule1")),
						},
					}
					(*existingLBInGoalState.FrontendIPConfigurations)[1].OutboundRules = &[]network.SubResource{
						{
							ID: to.StringPtr(fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s/outboundRules/%s", computeSubscriptionID, nodeResourceGroupName, slbName, slbOutboundRuleName)),
						},
					}
					lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(existingLBInGoalState, nil)
					lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(existingLBInGoalState, nil).Times(1)
					err := outboundConn.AssociateFrontEndIPConfigs(ctx, goal, goalIPs, nil)

					Expect(err).To(BeNil())
				})
			})
		})
	})
	Context("GetLoadBalancerAndValidateSKU", func() {
		It("should return error if GetLoadBalancer failed", func() {
			err := &cgerror.CategorizedError{
				Category:    apierror.InternalError,
				SubCode:     cgerror.Unknown,
				Dependency:  cgerror.ResourceType(""),
				OriginError: errors.New("get lb error"),
				Retriable:   to.BoolPtr(true),
			}
			lbClient.EXPECT().GetLoadBalancer(ctx, computeSubscriptionID, nodeResourceGroupName, slbName).Return(nil, err).Times(1)

			lb, cerr := outboundConn.GetLoadBalancerAndValidateSKU(ctx, goal)

			Expect(lb).To(BeNil())
			Expect(cerr).NotTo(BeNil())
			Expect(cerr.RetryNeeded()).To(BeTrue())
		})
		It("should return error if GetLoadBalancer failed: nrp host name is not resolved", func() {
			err := &cgerror.CategorizedError{
				Category:    apierror.InternalError,
				SubCode:     cgerror.Unknown,
				Dependency:  cgerror.ResourceType(""),
				OriginError: errors.New(string(armerror.NRPHostNameResolutionFailedMessage)),
				Retriable:   to.BoolPtr(true),
			}
			lbClient.EXPECT().GetLoadBalancer(ctx, computeSubscriptionID, nodeResourceGroupName, slbName).Return(nil, err).Times(1)

			lb, cerr := outboundConn.GetLoadBalancerAndValidateSKU(ctx, goal)

			Expect(lb).To(BeNil())
			Expect(cerr).NotTo(BeNil())
			Expect(cerr.RetryNeeded()).To(BeTrue())
		})

		It("should succeed if goal load balancer does not exist", func() {
			lbClient.EXPECT().GetLoadBalancer(ctx, computeSubscriptionID, nodeResourceGroupName, slbName).Return(nil, nil).Times(1)

			lb, rerr := outboundConn.GetLoadBalancerAndValidateSKU(ctx, goal)

			Expect(lb).To(BeNil())
			Expect(rerr).To(BeNil())
		})
		It("should return error if load balancer is not found", func() {

			lbClient.EXPECT().GetLoadBalancer(ctx, computeSubscriptionID, nodeResourceGroupName, slbName).Return(nil, cgerror.ToCategorizedError(fmt.Errorf("NotFound")).OverrideIfNotSet(http.StatusNotFound, apierror.ClientError, hcpEnums.ErrorCode_NotFound, "NotFound")).Times(1)

			lb, cerr := outboundConn.GetLoadBalancerAndValidateSKU(ctx, goal)

			Expect(lb).To(BeNil())
			Expect(cerr).To(BeNil())
		})

		It("should skip validation if load balancer is nil or empty", func() {
			lbClient.EXPECT().GetLoadBalancer(ctx, computeSubscriptionID, nodeResourceGroupName, slbName).Return(nil, cgerror.ToCategorizedError(fmt.Errorf("NotFound")).OverrideIfNotSet(http.StatusNotFound, apierror.ClientError, hcpEnums.ErrorCode_NotFound, "NotFound")).Times(1)
			lb, cerr := outboundConn.GetLoadBalancerAndValidateSKU(ctx, goal)

			Expect(lb).To(BeNil())
			Expect(cerr).To(BeNil())

			blb := &network.LoadBalancer{}
			lbClient.EXPECT().GetLoadBalancer(ctx, computeSubscriptionID, nodeResourceGroupName, slbName).Return(blb, cgerror.ToCategorizedError(fmt.Errorf("NotFound")).OverrideIfNotSet(http.StatusNotFound, apierror.ClientError, hcpEnums.ErrorCode_NotFound, "NotFound")).Times(1)
			lb, cerr = outboundConn.GetLoadBalancerAndValidateSKU(ctx, goal)

			Expect(lb).To(BeNil())
			Expect(cerr).To(BeNil())

		})

		It("should return error if existing load balancer is not Standard SKU", func() {
			slbID := fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s",
				computeSubscriptionID,
				nodeResourceGroupName,
				slbName)

			slb := &network.LoadBalancer{
				Name:     &slbName,
				ID:       &slbID,
				Location: &location,
				Sku: &network.LoadBalancerSku{
					Name: "Basic",
				},
				LoadBalancerPropertiesFormat: &network.LoadBalancerPropertiesFormat{
					ProvisioningState: network.ProvisioningStateSucceeded,
				},
			}

			lbClient.EXPECT().GetLoadBalancer(ctx, computeSubscriptionID, nodeResourceGroupName, slbName).Return(slb, nil).Times(1)

			lb, rerr := outboundConn.GetLoadBalancerAndValidateSKU(ctx, goal)

			Expect(lb).To(BeNil())
			Expect(rerr).NotTo(BeNil())
			Expect(rerr.SubCode).To(Equal(cgerror.NotStandardLoadBalancer))
			Expect(rerr.InnerMessage).To(ContainSubstring("is not a standard load balancer"))
		})

		It("should succeed if existing load balancer is Standard SKU", func() {
			slbID := fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s",
				computeSubscriptionID,
				nodeResourceGroupName,
				slbName)

			slb := &network.LoadBalancer{
				Name:     &slbName,
				ID:       &slbID,
				Location: &location,
				Sku: &network.LoadBalancerSku{
					Name: "Standard",
				},
				LoadBalancerPropertiesFormat: &network.LoadBalancerPropertiesFormat{
					ProvisioningState: network.ProvisioningStateSucceeded,
				},
			}

			lbClient.EXPECT().GetLoadBalancer(ctx, computeSubscriptionID, nodeResourceGroupName, slbName).Return(slb, nil).Times(1)

			lb, rerr := outboundConn.GetLoadBalancerAndValidateSKU(ctx, goal)

			Expect(lb).NotTo(BeNil())
			Expect(rerr).To(BeNil())
		})

		It("should succeed if existing load balancer doesn't have sku", func() {
			slbID := fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s",
				computeSubscriptionID,
				nodeResourceGroupName,
				slbName)

			slb := &network.LoadBalancer{
				Name:     &slbName,
				ID:       &slbID,
				Location: &location,
				Sku:      nil,
				LoadBalancerPropertiesFormat: &network.LoadBalancerPropertiesFormat{
					ProvisioningState: network.ProvisioningStateSucceeded,
				},
			}

			lbClient.EXPECT().GetLoadBalancer(ctx, computeSubscriptionID, nodeResourceGroupName, slbName).Return(slb, nil).Times(1)

			lb, rerr := outboundConn.GetLoadBalancerAndValidateSKU(ctx, goal)

			Expect(lb).To(BeNil())
			Expect(rerr).NotTo(BeNil())
			Expect(rerr.SubCode).To(Equal(cgerror.UnknownLoadBalancerSku))
			Expect(rerr.InnerMessage).To(ContainSubstring("does not have a SKU"))
		})
		It("should succeed if existing load balancer is not from the same location", func() {
			slbID := fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s",
				computeSubscriptionID,
				nodeResourceGroupName,
				slbName)

			slb := &network.LoadBalancer{
				Name:     &slbName,
				ID:       &slbID,
				Location: to.StringPtr("randomplace"),
				Sku: &network.LoadBalancerSku{
					Name: "Standard",
				},
				LoadBalancerPropertiesFormat: &network.LoadBalancerPropertiesFormat{
					ProvisioningState: network.ProvisioningStateSucceeded,
				},
			}

			lbClient.EXPECT().GetLoadBalancer(ctx, computeSubscriptionID, nodeResourceGroupName, slbName).Return(slb, nil).Times(1)

			lb, cerr := outboundConn.GetLoadBalancerAndValidateSKU(ctx, goal)

			Expect(lb).To(BeNil())
			Expect(cerr).NotTo(BeNil())
			Expect(cerr.SubCode).To(Equal(cgerror.UnexpectedStateLoadBalancer))
			Expect(cerr.InnerMessage).To(ContainSubstring("is different to expected location"))
		})
		When("load balancer is not in terminated state", func() {
			It("should report an error if failed to check if the load balancer is not in terminating state", func() {
				slbID := fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Network/loadBalancers/%s",
					computeSubscriptionID,
					nodeResourceGroupName,
					slbName)
				slb := &network.LoadBalancer{
					Name:     &slbName,
					ID:       &slbID,
					Location: &location,
					Sku: &network.LoadBalancerSku{
						Name: "Standard",
					},
					LoadBalancerPropertiesFormat: &network.LoadBalancerPropertiesFormat{
						ProvisioningState: network.ProvisioningStateDeleting,
					},
				}
				lbClient.EXPECT().GetLoadBalancer(ctx, computeSubscriptionID, nodeResourceGroupName, slbName).Return(slb, nil).Times(1)

				lb, rerr := outboundConn.GetLoadBalancerAndValidateSKU(ctx, goal)

				Expect(lb).To(BeNil())
				Expect(rerr).NotTo(BeNil())
				Expect(rerr.SubCode).To(Equal(cgerror.LBNotInTerminatingState))
				Expect(rerr.InnerMessage).To(ContainSubstring("is not in terminating state"))
			})
		})

		Context("getExcludedAgentPoolNames", func() {
			// Helper function to create agent pool with custom node labels
			createAgentPoolWithLabels := func(name string, labels map[string]string) *hcpAgentPool.AgentPoolResource {
				return &hcpAgentPool.AgentPoolResource{
					Properties: &hcpAgentPool.AgentPoolProfile{
						Name:             name,
						CustomNodeLabels: labels,
					},
				}
			}

			When("no agent pools exist", func() {
				It("should return empty excluded pools map", func() {
					agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return([]*hcpAgentPool.AgentPoolResource{}, nil).Times(1)

					excludedPools, err := outboundConn.getExcludedAgentPoolNames(ctx)

					Expect(err).To(BeNil())
					Expect(excludedPools).NotTo(BeNil())
					Expect(len(excludedPools)).To(Equal(0))
				})
			})

			When("agent pools exist without exclude label", func() {
				It("should return empty excluded pools map", func() {
					agentPools := []*hcpAgentPool.AgentPoolResource{
						createAgentPoolWithLabels("pool1", map[string]string{"other-label": "value"}),
						createAgentPoolWithLabels("pool2", map[string]string{}),
						createAgentPoolWithLabels("pool3", nil),
					}
					agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return(agentPools, nil).Times(1)

					excludedPools, err := outboundConn.getExcludedAgentPoolNames(ctx)

					Expect(err).To(BeNil())
					Expect(excludedPools).NotTo(BeNil())
					Expect(len(excludedPools)).To(Equal(0))
				})
			})

			When("some agent pools have exclude label", func() {
				It("should return only excluded pools", func() {
					agentPools := []*hcpAgentPool.AgentPoolResource{
						createAgentPoolWithLabels("pool1", map[string]string{"other-label": "value"}),
						createAgentPoolWithLabels("pool2", map[string]string{
							rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound: "true",
						}),
						createAgentPoolWithLabels("pool3", map[string]string{
							rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound: "",
							"other-label": "value",
						}),
						createAgentPoolWithLabels("pool4", map[string]string{"different-label": "value"}),
					}
					agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return(agentPools, nil).Times(1)

					excludedPools, err := outboundConn.getExcludedAgentPoolNames(ctx)

					Expect(err).To(BeNil())
					Expect(excludedPools).NotTo(BeNil())
					Expect(len(excludedPools)).To(Equal(2))
					Expect(excludedPools).To(HaveKey("pool2"))
					Expect(excludedPools).To(HaveKey("pool3"))
					Expect(excludedPools).NotTo(HaveKey("pool1"))
					Expect(excludedPools).NotTo(HaveKey("pool4"))
				})
			})

			When("all agent pools have exclude label", func() {
				It("should return all pools as excluded", func() {
					agentPools := []*hcpAgentPool.AgentPoolResource{
						createAgentPoolWithLabels("pool1", map[string]string{
							rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound: "true",
						}),
						createAgentPoolWithLabels("pool2", map[string]string{
							rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound: "false",
						}),
					}
					agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return(agentPools, nil).Times(1)

					excludedPools, err := outboundConn.getExcludedAgentPoolNames(ctx)

					Expect(err).To(BeNil())
					Expect(excludedPools).NotTo(BeNil())
					Expect(len(excludedPools)).To(Equal(2))
					Expect(excludedPools).To(HaveKey("pool1"))
					Expect(excludedPools).To(HaveKey("pool2"))
				})
			})

			When("ListAgentPools fails", func() {
				It("should return the error", func() {
					expectedError := cgerror.CreateInternalError()
					agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return(nil, expectedError).Times(1)

					excludedPools, err := outboundConn.getExcludedAgentPoolNames(ctx)

					Expect(excludedPools).To(BeNil())
					Expect(err).NotTo(BeNil())
					Expect(err).To(Equal(expectedError))
				})
			})

			When("agent pools have nil properties", func() {
				It("should skip pools with nil properties", func() {
					agentPools := []*hcpAgentPool.AgentPoolResource{
						{Properties: nil},
						createAgentPoolWithLabels("pool1", map[string]string{
							rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound: "true",
						}),
						{Properties: nil},
					}
					agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return(agentPools, nil).Times(1)

					excludedPools, err := outboundConn.getExcludedAgentPoolNames(ctx)

					Expect(err).To(BeNil())
					Expect(excludedPools).NotTo(BeNil())
					Expect(len(excludedPools)).To(Equal(1))
					Expect(excludedPools).To(HaveKey("pool1"))
				})
			})
		})

		Context("EnsureOutboundResources with excluded agent pools", func() {
			azureresources_test.DescribeTrack2ToggleMatrix(&toggle, "enable-sdk-track2-publicipaddresses", func(enableTrack2PIP bool) {
				azureresources_test.DescribeTrack2ToggleMatrix(&toggle, "enable-sdk-track2-slb-outbound-reconciler", func(enableTrack2VM bool) {
					When("agent pools are excluded from inbound backend pools", func() {
						It("should pass excluded agent pools to reconcilers", func() {
							// Create agent pools with some having the exclude label
							agentPools := []*hcpAgentPool.AgentPoolResource{
								{
									Properties: &hcpAgentPool.AgentPoolProfile{
										Name: "pool1",
										CustomNodeLabels: map[string]string{
											"normal-label": "value",
										},
									},
								},
								{
									Properties: &hcpAgentPool.AgentPoolProfile{
										Name: "excluded-pool1",
										CustomNodeLabels: map[string]string{
											rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound: "true",
										},
									},
								},
								{
									Properties: &hcpAgentPool.AgentPoolProfile{
										Name: "excluded-pool2",
										CustomNodeLabels: map[string]string{
											rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound: "",
											"other-label": "value",
										},
									},
								},
							}

							if enableTrack2PIP {
								pipClientTrack2.EXPECT().Get(testlib.IsContext(), gomock.Any(), gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientGetResponse{}, &cgerror.CategorizedError{HTTPStatus: to.IntPtr(http.StatusNotFound)})
								pipClientTrack2.EXPECT().CreateOrUpdate(testlib.IsContext(), nodeResourceGroupName, SLBTemporaryPIPName, gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientCreateOrUpdateResponse{
									PublicIPAddress: armnetwork.PublicIPAddress{Name: to.StringPtr(SLBTemporaryPIPName)},
								}, nil).Times(1)
							} else {
								pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
								pipClient.EXPECT().CreateOrUpdatePublicIPAddress(gomock.Any(), networkSubscriptionID, nodeResourceGroupName, SLBTemporaryPIPName, gomock.Any()).Return(&network.PublicIPAddress{
									Name: to.StringPtr(SLBTemporaryPIPName),
								}, nil).Times(1)
							}

							lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
							lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), networkSubscriptionID, nodeResourceGroupName, slbName, gomock.Any()).Return(existingLBInGoalState, nil).Times(1)
							agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return(agentPools, nil).Times(1)

							// Verify that excluded agent pools are passed to reconcilers
							vmssReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
								func(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
									Expect(len(backendpoolIDs)).To(Equal(2))
									Expect(len(backendpoolIDsIPV6)).To(Equal(2))
									Expect(len(excludedAgentPoolNames)).To(Equal(2))
									Expect(excludedAgentPoolNames).To(HaveKey("excluded-pool1"))
									Expect(excludedAgentPoolNames).To(HaveKey("excluded-pool2"))
									Expect(excludedAgentPoolNames).NotTo(HaveKey("pool1"))
									return nil
								}).Times(1)

							if enableTrack2VM {
								vmReconcilerTrack2.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
									func(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
										Expect(len(excludedAgentPoolNames)).To(Equal(2))
										Expect(excludedAgentPoolNames).To(HaveKey("excluded-pool1"))
										Expect(excludedAgentPoolNames).To(HaveKey("excluded-pool2"))
										return nil
									}).Times(1)
							} else {
								vmReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
									func(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
										Expect(len(excludedAgentPoolNames)).To(Equal(2))
										Expect(excludedAgentPoolNames).To(HaveKey("excluded-pool1"))
										Expect(excludedAgentPoolNames).To(HaveKey("excluded-pool2"))
										return nil
									}).Times(1)
							}

							err := outboundConn.EnsureOutboundResources(ctx, goal, nil, nil)
							Expect(err).To(BeNil())
						})

						It("should handle error from getExcludedAgentPoolNames", func() {
							expectedError := cgerror.CreateInternalError()
							agentPoolsRetriever.EXPECT().ListAgentPools(gomock.Any(), false).Return(nil, expectedError).Times(1)

							if enableTrack2PIP {
								pipClientTrack2.EXPECT().Get(testlib.IsContext(), gomock.Any(), gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientGetResponse{}, &cgerror.CategorizedError{HTTPStatus: to.IntPtr(http.StatusNotFound)})
								pipClientTrack2.EXPECT().CreateOrUpdate(testlib.IsContext(), nodeResourceGroupName, SLBTemporaryPIPName, gomock.Any(), nil).Return(armnetwork.PublicIPAddressesClientCreateOrUpdateResponse{
									PublicIPAddress: armnetwork.PublicIPAddress{Name: to.StringPtr(SLBTemporaryPIPName)},
								}, nil).Times(1)
							} else {
								pipClient.EXPECT().GetPublicIPAddress(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
								pipClient.EXPECT().CreateOrUpdatePublicIPAddress(gomock.Any(), networkSubscriptionID, nodeResourceGroupName, SLBTemporaryPIPName, gomock.Any()).Return(&network.PublicIPAddress{
									Name: to.StringPtr(SLBTemporaryPIPName),
								}, nil).Times(1)
							}

							lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
							lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), networkSubscriptionID, nodeResourceGroupName, slbName, gomock.Any()).Return(existingLBInGoalState, nil).Times(1)

							err := outboundConn.EnsureOutboundResources(ctx, goal, nil, nil)
							Expect(err).NotTo(BeNil())
							Expect(err).To(Equal(expectedError))
						})
					})
				})
			})
		})
	})
})

func generate50Tags() map[string]*string {
	res := map[string]*string{}
	for i := 0; i < 50; i++ {
		res["testkey"+strconv.Itoa(i)] = to.StringPtr("val" + strconv.Itoa(i))
	}
	return res
}
